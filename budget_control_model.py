# import xgboost as xgb
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, f1_score
from scipy.stats import ks_2samp



# Feature Engineering
def daily_feature_engineering(data):
    data['ds'] = pd.to_datetime(data['ds'])
    data['day_of_week'] = data['ds'].dt.dayofweek

    return data


def create_label_for_next_n_day(daily_data, n_day):
    result_data = daily_data.copy()
    result_data['ds'] = pd.to_datetime(result_data['ds'])
    result_data = result_data.sort_values(['account_id', 'adgroup_id', 'ds'])
    label_col = f'{n_day}_day_no_credit'
    result_data[label_col] = np.nan
    grouped = result_data.groupby(['account_id', 'adgroup_id'])

    rows_to_keep = []

    for (account_id, adgroup_id), group in grouped:
        # Keep original indices before reset
        original_indices = group.index.tolist()
        group = group.reset_index(drop=True)

        for i in range(len(group)):
            current_date = group.loc[i, 'ds']
            target_date = current_date + pd.Timedelta(days=n_day)
            future_row = group[group['ds'] == target_date]

            if not future_row.empty:
                # future_page_reservation_cost_with_people = future_row.iloc[0]['cur_day_page_reservation_cost_with_people']
                future_apply_pv = future_row.iloc[0]['cur_day_apply_pv']

                if pd.isna(future_apply_pv):
                    future_apply_pv = 0

                label_value = 1 if future_apply_pv == 0 else 0
                # Use original index to update result_data
                original_idx = original_indices[i]
                result_data.loc[original_idx, label_col] = label_value

                rows_to_keep.append(original_idx)

    # result_data = result_data.loc[rows_to_keep]

    result_data = result_data.reset_index(drop=True)

    return result_data

"""
def hourly_feature_engineering(hourly_data):
    result_data = hourly_data.copy()
    # result_data['is_auto_acquisition'] = (result_data['acquisition_cost'] * 0.01 > 100).astype(int)
    if 'acquisition_cost' in result_data.columns:
        result_data.drop(['acquisition_cost'], axis=1, inplace=True)
    # Create cyclic features
    result_data['hour_sin'] = np.sin(2 * np.pi * result_data['hour'] / 24)
    result_data['hour_cos'] = np.cos(2 * np.pi * result_data['hour'] / 24)

    result_data['ds'] = pd.to_datetime(result_data['ds'])
    result_data['ts'] = result_data['ds'] + pd.to_timedelta(result_data['hour'], unit='h')
    result_data = result_data.sort_values(['account_id', 'adgroup_id', 'ts']).reset_index(drop=True)
    
    metrics = ['cost', 'apply_pv', 'reservation_uv', 'valid_click_count', 'view_count']
    hour_windows = [2, 5, 6, 8, 10, 12, 14, 16, 18]

    def calculate_group_features(group):
        group = group.set_index('ts').sort_index()

        for window in hour_windows:
            for metric in metrics:
                # calculate cumulative sum
                col_name = f'cum_{window}h_up_to_cur_h_{metric}'
                group[col_name] = group[metric].rolling(
                    window=f'{window}h',
                    min_periods=1,
                    closed='left'
                ).sum().fillna(0)
                # calculate mean
                col_name = f'mean_{window}h_up_to_cur_h_{metric}'
                group[col_name] = group[metric].rolling(
                    window=f'{window}h',
                    min_periods=1,
                    closed='left'
                ).mean().fillna(0)

        return group.reset_index()

    # Process each adgroup separately with vectorized operations
    result_list = []
    for (account_id, adgroup_id), group in result_data.groupby(['account_id', 'adgroup_id']):
        processed_group = calculate_group_features(group)
        result_list.append(processed_group)

    # Concatenate all results
    result_data = pd.concat(result_list, ignore_index=True)
    # print(f"Total rows after cumulative sum: {result_data.shape[0]}")

    # Create features for metrics of previous hour
    for metric in metrics:
        sub_data = result_data.sort_values(by=['account_id', 'adgroup_id', 'ts'])
        merged = pd.merge(
                sub_data,
                sub_data,
                left_on=['account_id', 'adgroup_id', 'ts'],
                right_on=['account_id', 'adgroup_id', sub_data['ts'] + pd.Timedelta(hours=1)],
                suffixes=('', '_prev'),
                how='left'
            )
        merged = merged.fillna(0)
        result_data[f'prev_h_{metric}'] = merged[f"{metric}_prev"]
        # result_data['prev_h_page_reservation_cost_with_people'] = merged[f"page_reservation_cost_with_people_prev"]

    for metric in metrics:
        result_data[f'cum_2h_16h_increase_ratio_{metric}'] = (result_data[f'cum_2h_up_to_cur_h_{metric}'] - result_data[f'cum_16h_up_to_cur_h_{metric}']) / (result_data[f'cum_16h_up_to_cur_h_{metric}'] + 1e-9)
    
    all_metrics = [f'cum_16h_up_to_cur_h_{metric}' for metric in metrics] + [f'prev_h_{metric}' for metric in metrics] + [f'cum_12h_up_to_cur_h_{metric}' for metric in metrics]
    
    account_data = result_data.groupby(['account_id', 'ts']).agg({f'{metric}': 'sum' for metric in all_metrics}).reset_index()
    account_data.rename(columns={f'{metric}': f'account_{metric}' for metric in all_metrics}, inplace=True)
    result_data = pd.merge(result_data, account_data, on=['account_id', 'ts'], how='left')

    dapan_avg = result_data.groupby(['ts']).agg({f'{metric}': 'mean' for metric in all_metrics}).reset_index()
    dapan_avg.rename(columns={f'{metric}': f'dapan_{metric}' for metric in all_metrics}, inplace=True)
    result_data = pd.merge(result_data, dapan_avg, on=['ts'], how='left')

    for metric in all_metrics:
        result_data[f'diff_ratio_{metric}'] = (result_data[f'{metric}'] - result_data[f'dapan_{metric}']) / (result_data[f'dapan_{metric}'] + 1e-9)
        # result_data[f'diff_ratio_{metric}'] = (result_data[f'dapan_{metric}'] - result_data[f'{metric}']) / (result_data[f'{metric}'] + 1e-9)

    # Create squared, cubed features for metrics of previous hour
    for metric in metrics:
        result_data[f'prev_h_{metric}_squared'] = result_data[f'prev_h_{metric}'] ** 2
        result_data[f'prev_h_{metric}_cubed'] = result_data[f'prev_h_{metric}'] ** 3
    
    return result_data
"""


def hourly_feature_engineering(hourly_data):
    result_data = hourly_data.copy()
    # result_data['is_auto_acquisition'] = (result_data['acquisition_cost'] * 0.01 > 100).astype(int)
    if 'acquisition_cost' in result_data.columns:
        result_data.drop(['acquisition_cost'], axis=1, inplace=True)
    # Create cyclic features
    result_data['hour_sin'] = np.sin(2 * np.pi * result_data['hour'] / 24)
    result_data['hour_cos'] = np.cos(2 * np.pi * result_data['hour'] / 24)

    result_data['ds'] = pd.to_datetime(result_data['ds'])
    result_data['ts'] = result_data['ds'] + pd.to_timedelta(result_data['hour'], unit='h')
    result_data = result_data.sort_values(['account_id', 'adgroup_id', 'ts']).reset_index(drop=True)
    
    metrics = ['cost', 'apply_pv', 'reservation_uv', 'valid_click_count', 'view_count']
    hour_windows = [2, 5, 6, 8, 10, 12, 14, 16, 18]

    """
    def calculate_group_features(group):
        group = group.sort_values('ds')
        # group['ds'] = pd.to_datetime(group['ds'])
        for window in hour_windows:
            for metric in metrics:
                col_name = f'cum_{window}h_up_to_cur_h_{metric}'
                group[col_name] = 0

                for i, current_time in enumerate(group['ds']):
                    time_window_start = current_time - pd.Timedelta(hours=window)
                    group[col_name].iloc[i] = group.loc[(group['ds'] >= time_window_start) & (group['ds'] < current_time), metric].sum()

        return group
    """


    def calculate_group_features(group):
        group = group.set_index('ts').sort_index()

        for window in hour_windows:
            for metric in metrics:
                # calculate cumulative sum
                col_name = f'cum_{window}h_up_to_cur_h_{metric}'
                group[col_name] = group[metric].rolling(
                    window=f'{window}h',
                    min_periods=1,
                    closed='left'
                ).sum().fillna(0)
                # calculate mean
                col_name = f'mean_{window}h_up_to_cur_h_{metric}'
                group[col_name] = group[metric].rolling(
                    window=f'{window}h',
                    min_periods=1,
                    closed='left'
                ).mean().fillna(0)

        return group.reset_index()

    # Process each adgroup separately with vectorized operations
    """result_list = []
    for (account_id, adgroup_id), group in result_data.groupby(['account_id', 'adgroup_id']):
        processed_group = calculate_group_features(group)
        result_list.append(processed_group)"""
    # Parallelize the processing of each group
    def process_groups_in_parallel(data, n_jobs=-1):
        from joblib import Parallel, delayed
        # Group data by account_id and adgroup_id
        groups = data.groupby(['account_id', 'adgroup_id'])

        # Process each group in parallel using joblib
        result_list = Parallel(n_jobs=n_jobs)(
            delayed(calculate_group_features)(group) for _, group in groups
        )

        # Combine all processed groups back into a single DataFrame
        return pd.concat(result_list, ignore_index=True)

    # Concatenate all results
    # result_data = pd.concat(result_list, ignore_index=True)
    result_data = process_groups_in_parallel(result_data, n_jobs=4)
    # print(f"Total rows after cumulative sum: {result_data.shape[0]}")

    # Create features for metrics of previous hour
    for metric in metrics:
        """sub_data = result_data.sort_values(by=['account_id', 'adgroup_id', 'ts'])
        merged = pd.merge(
                sub_data,
                sub_data,
                left_on=['account_id', 'adgroup_id', 'ts'],
                right_on=['account_id', 'adgroup_id', sub_data['ts'] + pd.Timedelta(hours=1)],
                suffixes=('', '_prev'),
                how='left'
            )
        merged = merged.fillna(0)
        result_data[f'prev_h_{metric}'] = merged[f"{metric}_prev"]"""
        result_data[f'prev_h_{metric}'] = result_data.groupby(['account_id', 'adgroup_id'])[metric].shift(1).fillna(0)
        # result_data['prev_h_page_reservation_cost_with_people'] = merged[f"page_reservation_cost_with_people_prev"]
    
    for metric in metrics:
        result_data[f'cum_2h_16h_increase_ratio_{metric}'] = (result_data[f'cum_2h_up_to_cur_h_{metric}'] - result_data[f'cum_16h_up_to_cur_h_{metric}']) / (result_data[f'cum_16h_up_to_cur_h_{metric}'] + 1e-9)
     
    all_metrics = [f'cum_16h_up_to_cur_h_{metric}' for metric in metrics] + [f'prev_h_{metric}' for metric in metrics] + [f'cum_12h_up_to_cur_h_{metric}' for metric in metrics]
    
    account_data = result_data.groupby(['account_id', 'ts']).agg({f'{metric}': 'sum' for metric in all_metrics}).reset_index()
    account_data.rename(columns={f'{metric}': f'account_{metric}' for metric in all_metrics}, inplace=True)
    result_data = pd.merge(result_data, account_data, on=['account_id', 'ts'], how='left')

    dapan_avg = result_data.groupby(['ts']).agg({f'{metric}': 'mean' for metric in all_metrics}).reset_index()
    dapan_avg.rename(columns={f'{metric}': f'dapan_{metric}' for metric in all_metrics}, inplace=True)
    result_data = pd.merge(result_data, dapan_avg, on=['ts'], how='left')

    for metric in all_metrics:
        result_data[f'diff_ratio_{metric}'] = (result_data[f'{metric}'] - result_data[f'dapan_{metric}']) / (result_data[f'dapan_{metric}'] + 1e-9)

    # Create squared, cubed features for metrics of previous hour
    for metric in metrics:
        result_data[f'prev_h_{metric}_squared'] = result_data[f'prev_h_{metric}'] ** 2
        result_data[f'prev_h_{metric}_cubed'] = result_data[f'prev_h_{metric}'] ** 3
    
    return result_data

def split_data(data, label_col, n_weeks=2):
    # Remove rows with NaN in target variable
    data = data[data[label_col].notna()]
    
    data['ds'] = pd.to_datetime(data['ds'])
    
    
    if n_weeks == 0:
        test_start_date = data['ds'].max() + pd.DateOffset(days=2)
    
    else:
        test_start_date = data['ds'].max() - pd.DateOffset(weeks=n_weeks)
        print(f"Test start date: {test_start_date}") # 2025-06-23 00:00:00
        # print(f"Test start date: {test_start_date}") # 2025-06-08 00:00:00
        # test_start_date = data['ds'].max() - pd.DateOffset(weeks=1)
        # print(f"Test start date: {test_start_date}") # 2025-06-15 00:00:00
    test_data = data[data['ds'] >= test_start_date]
    train_data = data[data['ds'] < test_start_date]

    # Split the data into features and target
    # train_features = train_data.drop(['account_id', 'adgroup_id', 'ds', 'hour', 'ts', label_col], axis=1)
    train_target = train_data[label_col]
    train_data = train_data.drop([label_col], axis=1)
    # test_features = test_data.drop(['account_id', 'adgroup_id', 'ds', 'hour', 'ts', label_col], axis=1)
    test_target = test_data[label_col]
    test_data = test_data.drop([label_col], axis=1)
    print(f"label col: {label_col} \ntrain 1:0 counts: {train_target.sum()} vs {train_target.shape[0] - train_target.sum()} \n"
          f"test 1:0 counts: {test_target.sum()} vs {test_target.shape[0] - test_target.sum()}")

    return train_data, train_target, test_data, test_target


def prepare_data(hourly_data, daily_data, nday=1):
    hourly_data = hourly_data.copy()
    daily_data = daily_data.copy()
    # drop low cost entries
    # daily_data = daily_data[daily_data['cur_day_cost'] > biz_follow_cost_thres]
    # create label
    daily_data = create_label_for_next_n_day(daily_data, nday)
    print(f"Total rows in daily data before filtering: {daily_data.shape[0]}")

    """
    print("cur_day_page_reservation_cost_with_people vs label comparison:")
    comparison_df = daily_data[daily_data['cur_day_page_reservation_cost_with_people'] > 0]
    comparison_df = comparison_df[['cur_day_page_reservation_cost_with_people', f'{nday}_day_over_page_reservation_cost_with_people_thres']].head(40)
    print(comparison_df.to_string())
    """
    # daily_data = daily_data[daily_data[f'{nday}_day_over_page_reservation_cost_with_people_thres'].notna()]
    daily_data = daily_data[daily_data[f'{nday}_day_no_credit'].notna()]
    print(f"Total rows in daily data after filtering: {daily_data.shape[0]}")
    # search for cols names containing 'cur_day_' and drop them
    cur_day_cols = [col for col in daily_data.columns if 'cur_day_' in col]
    daily_data.drop(cur_day_cols, axis=1, inplace=True)
    # daily_data.to_csv(f'aggregated_adgroup_data_{nday}_day.csv', index=False)
    hourly_data = hourly_data.sort_values(['account_id', 'adgroup_id', 'ts']).reset_index(drop=True)
    print(f"Total rows in hourly data: {hourly_data.shape[0]}")
    #target_hours = [14, 15, 16, 17, 18, 19, 20]
    #hourly_data = hourly_data[hourly_data['hour'].isin(target_hours)]
    # print(f"Total rows in hourly data after filtering: {hourly_data.shape[0]}")
    # hourly_data.drop(['cost', 'acquisition_cost', 'view_count', 'valid_click_count', 'biz_follow_uv', 'biz_reg_uv', 'reservation_uv', 'unique_dynamic_creative_count'], axis=1, inplace=True)
    hourly_data.drop(['cost', 'view_count', 'valid_click_count', 'apply_pv', 'reservation_uv', 'unique_dynamic_creative_count'], axis=1, inplace=True)
    merged_data = hourly_data.merge(daily_data, on=['account_id', 'adgroup_id', 'ds'], how='left')
    merged_data.to_csv(f'{nday}_day_merged_data.csv', index=False)
    print(f"Total rows in merged data: {merged_data.shape[0]}")
    return merged_data


def train_model(train_data, train_target):
    """ # exclude hours after 23:00 and before 6:00
    data = data[data['hour'] < 23]
    data = data[data['hour'] >= 6]
    """
    scale = 0.2
    model = xgb.XGBClassifier(objective='binary:logistic', random_state=42, alpha=0.2, scale_pos_weight=scale,
                             n_estimators=100, max_depth=2, learning_rate=0.1, use_label_encoder=False)
    # import lightgbm as lgb 
    # model = lgb.LGBMClassifier(objective='binary', random_state=42,
    #                           n_estimators=100, max_depth=3, learning_rate=0.1)
    train_features = train_data.drop(['account_id', 'adgroup_id', 'ds', 'hour', 'ts'], axis=1)
    model.fit(train_features, train_target)

    return model


def evaluate_model(model, data, target, data_type):
    if len(data) == 0:
        print(f"No data for {data_type}")
        return pd.DataFrame()
    # feature importance
    feature_importance = model.feature_importances_
    features = data.drop(['account_id', 'adgroup_id', 'ds', 'hour', 'ts'], axis=1)
    importance_df = pd.DataFrame({
    'Feature': features.columns,
    'Importance': feature_importance}).sort_values(by='Importance', ascending=False)
    print(importance_df)

    y_pred = model.predict_proba(features)[:, 1]
    auc = roc_auc_score(target, y_pred)
    print(f"{data_type} AUC: {auc}")

    class0 = y_pred[target == 0]
    class1 = y_pred[target == 1]
    ks = ks_2samp(class0, class1)
    print(f"{data_type} KS: {ks.statistic:.4f} p-value: {ks.pvalue}")

    f1 = f1_score(target, (y_pred > 0.5).astype(int))
    print(f"{data_type} F1: {f1:.4f}")
    
    res_df = data.copy()
    res_df['predict_score'] = y_pred
    
    return res_df
    

def main(save_dir='samples/'):
    print("===Loading data...")
    source_dir = 'samples/'
    save_dir = 'samples/'

    import os

    hourly_data = pd.read_csv(os.path.join(source_dir, 'aggregated_adgroup_hourly_data.csv'))
    daily_data = pd.read_csv(os.path.join(source_dir, 'aggregated_adgroup_daily_data.csv'))
    print("===Feature engineering...")
    hourly_data = hourly_feature_engineering(hourly_data)
    hourly_data.to_csv(os.path.join(save_dir, 'feature_aggregated_adgroup_hourly_data.csv'), index=False)
    daily_data = daily_feature_engineering(daily_data)
    daily_data.to_csv(os.path.join(save_dir, 'feature_aggregated_adgroup_daily_data.csv'), index=False)
    n = 0 # train for next n days, 0 = same day prediction
    print(f"===Preparing data for next {n} day...")
    full_data = prepare_data(hourly_data, daily_data, n)

    #select_cols = [str(x.strip()) for x in open("selected_features.txt").readlines()]
    #features = features[select_cols]

    # train_data, train_target, test_data, test_target = split_data(full_data, f'{n}_day_over_page_reservation_cost_with_people_thres', n_weeks=2)
    train_data, train_target, test_data, test_target = split_data(full_data, 
                                                                  # f'{n}_day_over_page_reservation_cost_with_people_thres', 
                                                                  f'{n}_day_no_credit',
                                                                  n_weeks=0)
    train_set = train_data.copy()
    train_set[f'{n}_day_no_credit'] = train_target
    from feature_filter import filter_features_comprehensive
    res = filter_features_comprehensive(train_set, 
                                        f'{n}_day_no_credit',
                                        correlation_threshold = 0.95, 
                                        target_correlation_min = 0.01,
                                        variance_threshold = 0.01,
                                        iv_threshold = 0.02,
                                        missing_threshold = 0.9,
                                        categorical_threshold= 20,
                                        verbose=True)
    selected_features = res['selected_features']
    if 'hour' not in selected_features:
        selected_features.append('hour')
    full_data[selected_features+[f'{n}_day_no_credit']].to_csv(os.path.join(save_dir, f'{n}_day_filtered_data.csv'), index=False)
    train_data = train_data[selected_features]
    test_data = test_data[selected_features]
    with open(os.path.join(save_dir, 'selected_features.txt'), 'w') as f:
        f.write("\n".join(selected_features))

    print(f"===Training for next {n} day...\n")
    model = train_model(train_data, train_target)
    model.save_model(os.path.join(save_dir, f'{n}_day_model.json'))
    # use previously saved model
    # model = xgb.Booster()
    # model.load_model(os.path.join(save_dir, f'{n}_day_model.json'))
    res_df = evaluate_model(model, train_data, train_target, 'Train')
    res_df.to_csv(os.path.join(save_dir, f'{n}_day_predict_result_train.csv'))
    if len(test_data) == 0:
        return
    res_df = evaluate_model(model, test_data, test_target, 'Test')
    res_df.to_csv(os.path.join(save_dir, f'{n}_day_predict_result_test.csv'))


if __name__ == "__main__":
    main()