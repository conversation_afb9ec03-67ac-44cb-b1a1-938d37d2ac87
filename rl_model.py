import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from collections import deque
import random

# Load and preprocess the data
def load_data(file_path):
    """
    Load the data containing hourly advertising information.
    The data should be sorted by ['account_id', 'adgroup_id', 'ts'].
    """
    df = pd.read_csv(file_path)
    df['ts'] = pd.to_datetime(df['ts'])  # Ensure timestamp is in datetime format
    return df


# Define the environment
class AdGroupEnvironment:
    def __init__(self, data, account_id, adgroup_id, feature_columns):
        """
        Parameters:
        - data: DataFrame containing advertising info
        - account_id: The advertiser's account ID
        - adgroup_id: The ad group ID to optimize
        - feature_columns: List of feature column names to include in the state
        """
        self.data = data[
            (data['account_id'] == account_id) & (data['adgroup_id'] == adgroup_id)
        ].reset_index(drop=True)
        self.feature_columns = feature_columns
        self.current_step = 0

    def reset(self):
        """Reset the environment to the first time step."""
        self.current_step = 0
        return self.get_state()

    def get_state(self):
        """Return the current state as a feature vector."""
        return self.data.iloc[self.current_step][self.feature_columns].values

    def step(self, action):
        """
        Take an action and return the new state, reward, and done flag.
        Action: Adjust the cost (e.g., increase, decrease, or keep it the same).
        """
        # Apply the action to adjust the cost
        # @todo: compute for the rest of the day instead of for just one hour 
        base_cost = self.data.iloc[self.current_step]['cost']
        if action == 0: 
            adjusted_cost = 0
        elif action == 1: 
            adjusted_cost = base_cost 
        

        # Immediate reward: reservation_uv
        immediate_reward = self.data.iloc[self.current_step]['reservation_uv']

        # Long-term reward
        long_term_reward = -1 * self.data.iloc[self.current_step]['cost'] / (self.data.iloc[self.current_step]['apply_pv'] + 1e-9)

        # Total reward
        reward = immediate_reward + long_term_reward

        # Move to the next time step
        self.current_step += 1
        done = self.current_step >= len(self.data)

        # Get the next state
        next_state = self.get_state() if not done else None
        return next_state, reward, done


# Define the DQN agent
class DQNAgent:
    def __init__(self, state_size, action_size):
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=2000)
        self.gamma = 0.95  # Discount factor
        self.epsilon = 1.0  # Exploration rate
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = 0.001
        self.model = self.build_model()
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
        self.criterion = nn.MSELoss()

    def build_model(self):
        """Build a neural network for Q-learning."""
        model = nn.Sequential(
            nn.Linear(self.state_size, 64),
            nn.ReLU(),
            nn.Linear(64, 64),
            nn.ReLU(),
            nn.Linear(64, self.action_size)
        )
        return model

    def remember(self, state, action, reward, next_state, done):
        """Store experience in memory."""
        self.memory.append((state, action, reward, next_state, done))

    def act(self, state):
        """Select an action using epsilon-greedy policy."""
        if np.random.rand() <= self.epsilon:
            return random.randrange(self.action_size)  # Random action
        state = torch.FloatTensor(state).unsqueeze(0)
        with torch.no_grad():
            q_values = self.model(state)
        return torch.argmax(q_values).item()  # Best action

    def replay(self, batch_size):
        """Train the model using random experiences from memory."""
        if len(self.memory) < batch_size:
            return

        minibatch = random.sample(self.memory, batch_size)
        for state, action, reward, next_state, done in minibatch:
            state = torch.FloatTensor(state).unsqueeze(0)
            next_state = torch.FloatTensor(next_state).unsqueeze(0) if next_state is not None else None
            reward = torch.tensor(reward, dtype=torch.float32)

            # Compute the target
            target = reward
            if not done:
                with torch.no_grad():
                    target += self.gamma * torch.max(self.model(next_state))

            # Compute the current Q-value
            q_values = self.model(state)
            current_q = q_values[0, action]

            # Compute the loss
            loss = self.criterion(current_q, target)

            # Backpropagation
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()

        # Decay epsilon (exploration rate)
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay


# Train the agent for each adgroup_id
def train_agent(data, account_id, adgroup_id, features, episodes=1000, batch_size=32):
    env = AdGroupEnvironment(data, account_id, adgroup_id, features)
    state_size = len(features)
    action_size = 3  # Example: [decrease cost, keep cost, increase cost]
    agent = DQNAgent(state_size, action_size)

    for e in range(episodes):
        state = env.reset()
        state = np.reshape(state, [1, state_size])
        total_reward = 0
        for time in range(len(env.data)):
            action = agent.act(state)
            next_state, reward, done = env.step(action)
            total_reward += reward
            next_state = np.reshape(next_state, [1, state_size]) if not done else None
            agent.remember(state, action, reward, next_state, done)
            state = next_state
            if done:
                print(f"Episode {e+1}/{episodes} - Reward: {total_reward}")
                break
        agent.replay(batch_size)


# Example usage
if __name__ == "__main__":
    # Load data
    file_path = 'advertising_data.csv'  # Replace with your data file
    data = load_data(file_path)

    # Define feature columns (all columns except account_id, adgroup_id, ts, reservation_uv, apply_pv)
    feature_columns = [col for col in data.columns if col not in ['account_id', 'adgroup_id', 'ts', 'reservation_uv', 'apply_pv']]

    # Train agent for each adgroup_id under a given account_id
    account_ids = []  
    for account_id in account_ids:
        adgroup_ids = data[data['account_id'] == account_id]['adgroup_id'].unique()
        for adgroup_id in adgroup_ids:
            print(f"Training for adgroup_id: {adgroup_id}")
            train_agent(data, account_id, adgroup_id, feature_columns)