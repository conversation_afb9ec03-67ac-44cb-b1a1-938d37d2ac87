import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict
import sys

def load_and_parse_data(csv_file):
    try:
        df = pd.read_csv(csv_file)
        print(f"Loaded {len(df)} rows from {csv_file}")
        print(f"Columns: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        sys.exit(1)

def aggregate_by_adgroup(df):
    df['cost'] = df['cost'] * 0.01
    grouped = df.groupby(['account_id', 'adgroup_id', 'ds', 'hour'])    
    aggregated = grouped.agg({
        'dynamic_creative_id': 'nunique',  # Count of unique dynamic_creative_ids
        'cost': 'sum',
        # 'acquisition_cost': 'sum',
        'view_count': 'sum',
        'valid_click_count': 'sum',
        'reservation_uv': 'sum',
        'apply_pv': 'sum'
    }).reset_index()
    
    # Rename col name
    aggregated.rename(columns={'dynamic_creative_id': 'unique_dynamic_creative_count'}, inplace=True)
    
    return aggregated

def calculate_daily_totals(df):
    daily_totals = df.groupby(['account_id', 'adgroup_id', 'ds']).agg({
        'cost': 'sum',
        # 'acquisition_cost': 'sum',
        'view_count': 'sum',
        'valid_click_count': 'sum',
        'reservation_uv': 'sum',
        'apply_pv': 'sum'
    }).reset_index()
    
    # avoid zero division 
    daily_totals['apply_cost'] = np.where(daily_totals['apply_pv'] == 0, 0, daily_totals['cost'] / daily_totals['apply_pv'])
    daily_totals['page_reservation_cost_with_people'] = np.where(daily_totals['reservation_uv'] == 0, 0, daily_totals['cost'] / daily_totals['reservation_uv'])

    # metric_columns = ['cost', 'acquisition_cost', 'view_count', 'valid_click_count', 'biz_follow_uv', 'biz_reg_uv', 'reservation_uv', 'biz_follow_cost', 'biz_biz_follow_cost', 'page_reservation_cost_with_people']
    metric_columns = ['cost', 'view_count', 'valid_click_count', 'reservation_uv', 'apply_pv', 'page_reservation_cost_with_people', 'apply_cost']

    rename_dict = {col: f'cur_day_{col}' for col in metric_columns}
    daily_totals = daily_totals.rename(columns=rename_dict)

    return daily_totals

def calculate_historical_totals(df, days_back):
    df['ds'] = pd.to_datetime(df['ds'])

    # adgroup level stat

    historical_data = []

    grouped_data = df.groupby(['account_id', 'adgroup_id'])

    for (account_id, adgroup_id), group_data in grouped_data:
        dates_for_group = sorted(group_data['ds'].unique())


        for current_date in dates_for_group:
            current_date = pd.to_datetime(current_date)
            start_date = current_date - timedelta(days=days_back)
            end_date = current_date - timedelta(days=1)  # Exclude current day
            historical_period_data = group_data[
                (group_data['ds'] >= start_date) &
                (group_data['ds'] <= end_date)
            ]

            if len(historical_period_data) > 0:
                totals = historical_period_data.agg({
                    'cost': 'sum',
                    # 'acquisition_cost': 'sum',
                    'view_count': 'sum',
                    'valid_click_count': 'sum',
                    'reservation_uv': 'sum',
                    'apply_pv': 'sum',

                })

            else:
                # If no historical data, set all values to 0
                totals = pd.Series({
                    'cost': 0,
                    # 'acquisition_cost': 0,
                    'view_count': 0,
                    'valid_click_count': 0,
                    'reservation_uv': 0,
                    'apply_pv': 0
                })
            # calculate cost per pv
            
            
            if totals['reservation_uv'] != 0:
                totals['page_reservation_cost_with_people'] = totals['cost'] / totals['reservation_uv']
            else:
                totals['page_reservation_cost_with_people'] = 0
            if totals['apply_pv'] != 0:
                totals['apply_cost'] = totals['cost'] / totals['apply_pv']
            else:
                totals['apply_cost'] = 0
            
            historical_data.append({
                'account_id': account_id,
                'adgroup_id': adgroup_id,
                'ds': current_date,
                **{f'past_{days_back}day_{col}': totals[col] for col in totals.index}
            })

    # return pd.DataFrame(historical_data)
    historical_df = pd.DataFrame(historical_data)

    # account level stat
    historical_data = []
    grouped_data = df.groupby(['account_id'])

    for (account_id), group_data in grouped_data:
        dates_for_group = sorted(group_data['ds'].unique())

        for current_date in dates_for_group:
            current_date = pd.to_datetime(current_date)
            start_date = current_date - timedelta(days=days_back)
            end_date = current_date - timedelta(days=1)  # Exclude current day
            historical_period_data = group_data[
                (group_data['ds'] >= start_date) &
                (group_data['ds'] <= end_date)
            ]

            if len(historical_period_data) > 0:
                totals = historical_period_data.agg({
                    'cost': 'sum',
                    # 'acquisition_cost': 'sum',
                    'view_count': 'sum',
                    'valid_click_count': 'sum',
                    'reservation_uv': 'sum',
                    'apply_pv': 'sum',

                })

            else:
                # If no historical data, set all values to 0
                totals = pd.Series({
                    'cost': 0,
                    # 'acquisition_cost': 0,
                    'view_count': 0,
                    'valid_click_count': 0,
                    'reservation_uv': 0,
                    'apply_pv': 0
                })
            # calculate cost per pv
            
            
            if totals['reservation_uv'] != 0:
                totals['page_reservation_cost_with_people'] = totals['cost'] / totals['reservation_uv']
            else:
                totals['page_reservation_cost_with_people'] = 0
            if totals['apply_pv'] != 0:
                totals['apply_cost'] = totals['cost'] / totals['apply_pv']
            else:
                totals['apply_cost'] = 0
            
            historical_data.append({
                'account_id': account_id,
                'ds': current_date,
                **{f'account_past_{days_back}day_{col}': totals[col] for col in totals.index}
            })

    account_df = pd.DataFrame(historical_data)

    # dapan level stat

    metrics = ['cost', 'view_count', 'valid_click_count', 'reservation_uv', 'apply_pv']

    totals = df.groupby(['ds']).agg({
        'cost': 'sum',
        'view_count': 'sum',
        'valid_click_count': 'sum',
        'reservation_uv': 'sum',
        'apply_pv': 'sum'
    }).reset_index()

    totals['page_reservation_cost_with_people'] = totals['cost'] / (totals['reservation_uv'] + 1e-9)
    totals['apply_cost'] = totals['cost'] / (totals['apply_pv'] + 1e-9)

    dapan_df = totals[['ds', 'apply_cost', 'page_reservation_cost_with_people']]
    dapan_prev = dapan_df.copy()
    dapan_prev['ds'] = dapan_prev['ds'] + pd.Timedelta(days=1)
    dapan_df = pd.merge(dapan_df, dapan_prev, on='ds', how='left', suffixes=('', '_prev'))
    dapan_df.drop(columns=['apply_cost', 'page_reservation_cost_with_people'], inplace=True)
    dapan_df.rename(columns={f"{metric}_prev": f"dapan_cum_to_past_1day_{metric}" for metric in ['apply_cost', 'page_reservation_cost_with_people']}, inplace=True)

    historical_df = pd.merge(historical_df, account_df, how='left', on=['account_id', 'ds'])
    historical_df = pd.merge(historical_df, dapan_df, how='left', on=['ds'])

    return historical_df


def main(save_dir='samples/'):
    file_dir = '../data_dynamic_creative_level/hourly_reports_REQUEST_TIME/'
    save_dir = 'samples/'
    lines = open('../xiaochengxu_accounts.txt').readlines()
    accounts_list = [str(l.strip()) for l in lines]
    import os
    
    # Load the data
    print("Loading data...")
    all_df = []
    for file in os.listdir(file_dir):
        if file.endswith('.csv'):
            file_path = os.path.join(file_dir, file)
            if file.split('.')[0] not in accounts_list:
                continue
            print(f"Loading {file_path}...")
            df = load_and_parse_data(file_path)
            all_df.append(df)
    df = pd.concat(all_df, ignore_index=True)
    
    # Convert date to datetime
    df['ds'] = pd.to_datetime(df['ds'])
    
    # Aggregate by adgroup
    print("Aggregating data by adgroup...")
    aggregated = aggregate_by_adgroup(df)

    # write aggregated hourly report to csv
    aggregated.to_csv(os.path.join(save_dir, 'aggregated_adgroup_hourly_data.csv'), index=False)
    
    print("Calculating daily totals...")
    daily_totals = calculate_daily_totals(df)
    
    print("Calculating historical totals...")
    past_1day = calculate_historical_totals(aggregated, 1)
    past_3day = calculate_historical_totals(aggregated, 3)
    past_7day = calculate_historical_totals(aggregated, 7)
    
    past_3day.drop(columns=['dapan_cum_to_past_1day_apply_cost', 'dapan_cum_to_past_1day_page_reservation_cost_with_people'], inplace=True)
    past_7day.drop(columns=['dapan_cum_to_past_1day_apply_cost', 'dapan_cum_to_past_1day_page_reservation_cost_with_people'], inplace=True)
    
    # Merge all data together
    print("Merging all data...")
    result = daily_totals.copy()
    
    # Merge daily totals
    # result = result.merge(daily_totals, on=['account_id', 'adgroup_id', 'ds'], how='left')
    
    result = result.merge(past_1day, on=['account_id', 'adgroup_id', 'ds'], how='left')
    result = result.merge(past_3day, on=['account_id', 'adgroup_id', 'ds'], how='left')
    result = result.merge(past_7day, on=['account_id', 'adgroup_id', 'ds'], how='left')

    print(f"Columns after merge: {list(result.columns)}")
    
    result = result.fillna(0)
    
    # Sort by account_id, adgroup_id, date, hour (if hour column exists)
    sort_columns = ['account_id', 'adgroup_id', 'ds']
    if 'hour' in result.columns:
        sort_columns.append('hour')
    result = result.sort_values(sort_columns)
    
    output_file = os.path.join(save_dir, 'aggregated_adgroup_daily_data.csv')
    result.to_csv(output_file, index=False)
    
    print(f"Aggregated data saved to {output_file}")
    print(f"Total rows in output: {len(result)}")
    print(f"Columns in output: {list(result.columns)}")
    
    print("\nSample of aggregated data:")
    print(result.head(10).to_string())

if __name__ == "__main__":
    main()
