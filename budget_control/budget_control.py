import datetime
import json


def get_pre_effect_limits(account_status, scale_ratio, control_config):
    dapan_reservation_cost_14 = account_status['past2WeeksReservationCost']  # 表单成本
    dapan_apply_cost_14 = account_status['past2WeeksApplyCost']  # 完件表单成本
    print(f"大盘前效数据: 14天表单成本：{dapan_reservation_cost_14}; "
          f"14天完件成本{dapan_apply_cost_14}")

    # 配置
    step_1_final_cost_goal = int(dapan_reservation_cost_14 * scale_ratio)
    step_2_final_cost_goal = int(dapan_apply_cost_14 * scale_ratio)

    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    if hour < 12:  # 上午适当放开成本约束系数
        adgroup_step_1_cost_limit = step_1_final_cost_goal * control_config.STEP_1_COST_LIMIT_SCALE * control_config.MORNING_SCALE
        adgroup_step_2_cost_limit = step_2_final_cost_goal * control_config.STEP_2_COST_LIMIT_SCALE * control_config.MORNING_SCALE
    elif hour >= 12 and hour < 20:  # 上午适当放开成本约束系数
        adgroup_step_1_cost_limit = step_1_final_cost_goal * control_config.STEP_1_COST_LIMIT_SCALE * control_config.AFTERNOON_SCALE
        adgroup_step_2_cost_limit = step_2_final_cost_goal * control_config.STEP_2_COST_LIMIT_SCALE * control_config.AFTERNOON_SCALE
    else:
        adgroup_step_1_cost_limit = step_1_final_cost_goal * control_config.STEP_1_COST_LIMIT_SCALE * control_config.NIGHT_SCALE
        adgroup_step_2_cost_limit = step_2_final_cost_goal * control_config.STEP_2_COST_LIMIT_SCALE * control_config.NIGHT_SCALE

    print('--- 目标表单成本： ', step_1_final_cost_goal)
    print('--- 目标完件成本： ', step_2_final_cost_goal)

    print('--- 表单成本限制： ', adgroup_step_1_cost_limit)
    print('--- 目标完件成本限制： ', adgroup_step_2_cost_limit)

    return {"adgroup_step_1_cost_limit": adgroup_step_1_cost_limit,
            "adgroup_step_2_cost_limit": adgroup_step_2_cost_limit,
            }


def get_check_pre_effect_thres(account_status, control_config):
    dapan_apply_cost_14 = account_status['past2WeeksApplyCost']  # 完件表单成本
    check_cost_thres = dapan_apply_cost_14 * control_config.CHECK_PRE_THRESHOLD_RATIO
    print(f"前效控停阈值: {check_cost_thres}")
    return check_cost_thres


def check_pre_effect(data, account_id, adgroup_id, check_cost_thres, pre_effect_limits, data_header):
    # today_cost = data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, data_header.COST_POSTFIX])
    # today_step_1_cost = data_header.JOIN_CHAR.join(
    #     [data_header.TODAY_PREFIX, data_header.STEP_1_STAT_NAME, data_header.COST_POSTFIX])
    today_cost = data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, data_header.COST_POSTFIX])
    today_step_2_cost = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_2_STAT_NAME, data_header.COST_POSTFIX])
    today_step_1_cost = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_1_STAT_NAME, data_header.COST_POSTFIX])
    today_step_2_uv = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_2_UV])
    """if data[today_cost] > check_cost_thres:
        if data[today_step_3_cost] > pre_effect_limits['adgroup_step_3_cost_limit']:
            if (data[today_step_1_cost] > pre_effect_limits['adgroup_step_1_cost_limit'] or
                    data[today_step_2_cost] > pre_effect_limits['adgroup_step_2_cost_limit']):
                print(account_id, adgroup_id, '前效控停')
                return True"""
    if data[today_step_2_uv] > 0:  # 当天有完件
        if data[today_step_2_cost] > pre_effect_limits['adgroup_step_2_cost_limit'] * 1: # 2倍完件成本
            print(account_id, adgroup_id, '前效控停')
            msg = '--- 当天有完件，完件表单成本 %.2f 大于 2 倍限制 %.2f' % (data[today_step_2_cost], pre_effect_limits['adgroup_step_2_cost_limit'])
            return True, msg
        if data.get(today_step_1_cost) and data[today_step_1_cost] > 0:
            if data[today_step_1_cost] > pre_effect_limits['adgroup_step_1_cost_limit']: # 2倍表单成本
                print(account_id, adgroup_id, '前效控停')
                msg = '--- 当天有完件，表单成本 %.2f 大于 2 倍限制 %.2f' % (data[today_step_1_cost], pre_effect_limits['adgroup_step_1_cost_limit'])
                return True, msg
    else:  # 当天无完件
        if data[today_cost] > pre_effect_limits['adgroup_step_2_cost_limit']: # 2倍完件成本
            print(account_id, adgroup_id, '前效控停')
            msg = '--- 当天无完件，当天消耗 %.2f 大于 2 倍限制 %.2f' % (data[today_step_2_cost], pre_effect_limits['adgroup_step_2_cost_limit'])
            return True, msg
        if data.get(today_step_1_cost) and data[today_step_1_cost] > 0:
            if data[today_step_1_cost] > pre_effect_limits['adgroup_step_1_cost_limit']: # 2倍表单成本
                print(account_id, adgroup_id, '前效控停')
                msg = '--- 当天无完件，表单成本 %.2f 大于 2 倍限制 %.2f' % (data[today_step_1_cost], pre_effect_limits['adgroup_step_1_cost_limit'])
                return True, msg
        else:
            if data[today_cost] > pre_effect_limits['adgroup_step_1_cost_limit']: # 2倍表单成本
                print(account_id, adgroup_id, '前效控停')
                msg = '--- 当天无完件无表单，当天消耗 %.2f 大于 2 倍表单成本限制 %.2f' % (data[today_cost], pre_effect_limits['adgroup_step_1_cost_limit'])
                return True, msg
    return False, ''


def check_suspend(data, adgroup_past_houxiao_data, account_id, adgroup_id, pre_effect_limits, post_effect_limits, data_header):
    
    life_long_days = data[data_header.LIFE_LONG_DAYS]
    past_3_day_step_1_cost = data_header.JOIN_CHAR.join(
        [data_header.PAST_3_DAYS_PREFIX, data_header.STEP_1_STAT_NAME, data_header.COST_POSTFIX])
    past_7_day_cost = data_header.JOIN_CHAR.join(
        [data_header.PAST_7_DAYS_PREFIX, data_header.COST_POSTFIX])
    # print(f"data: {data}")
    # print(f"houxiao data: {adgroup_past_houxiao_data}")
    
    if life_long_days >= 3 and data.get(past_3_day_step_1_cost) and data[past_3_day_step_1_cost] > pre_effect_limits['adgroup_step_2_cost_limit'] * 0.5: # 1倍表单成本
        print(account_id, adgroup_id, '暂停（30天）')
        msg = '--- 3天内表单成本 %.2f 大于 1倍授信成本 %.2f' % (data[past_3_day_step_1_cost], pre_effect_limits['adgroup_step_2_cost_limit'] * 0.5)
        return True, msg
    if life_long_days >= 7 and data.get(past_7_day_cost) and data[past_7_day_cost] < 100:
        print(account_id, adgroup_id, '暂停（30天）')
        msg = '--- 7天内消耗 %.2f 小于 100' % (data[past_7_day_cost])
        return True, msg
    dapan_d7_cost_rate_14, dapan_d7_apply_cost_14 = post_effect_limits['dapan_d7_cost_rate_14'], \
    post_effect_limits['dapan_d7_apply_cost_14']
    print(f"暂停后效配置: {dapan_d7_cost_rate_14, dapan_d7_apply_cost_14}")
    
    if life_long_days >= 3:
        if adgroup_past_houxiao_data['past3DaysD7ApplyPv'] > 10 and adgroup_past_houxiao_data['past3DaysD7ShouxinUv'] < 1:
            print(account_id, adgroup_id, '暂停（30天）')
            msg = '--- 3天内进件>10, 没有授信'
            return True, msg
    
    if life_long_days >= 7:
        if adgroup_past_houxiao_data['past7DaysD7ApplyPv'] > 20 and adgroup_past_houxiao_data['past7DaysD7ShouxinUv'] < 1:
            print(account_id, adgroup_id, '暂停（30天）')
            msg = '--- 7天内进件>20, 没有授信'
            return True, msg
    
    if life_long_days >= 7:
        if adgroup_past_houxiao_data['past2WeeksD7CostRate'] > dapan_d7_cost_rate_14 * 3:
            print(account_id, adgroup_id, '暂停（30天）')
            msg = '--- 14天费率 %.2f 大于3倍大盘 %.2f' % (adgroup_past_houxiao_data['aveD7CostRate'], dapan_d7_cost_rate_14)
            return True, msg
    if life_long_days >= 3:
        if adgroup_past_houxiao_data['past3DaysD7ApplyCost'] > dapan_d7_apply_cost_14 * 2:
            print(account_id, adgroup_id, '暂停（30天）')
            msg = '--- 3天进件成本 %.2f 大于2倍大盘 %.2f' % (adgroup_past_houxiao_data['past3DaysD7ApplyCost'], dapan_d7_apply_cost_14)
            return True, msg
    if life_long_days >= 7:
        if adgroup_past_houxiao_data['past7DaysD7ApplyCost'] > dapan_d7_apply_cost_14 * 1.5:
            print(account_id, adgroup_id, '暂停（30天）')
            msg = '--- 7天进件成本 %.2f 大于1.5倍大盘 %.2f' % (adgroup_past_houxiao_data['past7DaysD7ApplyCost'], dapan_d7_apply_cost_14)
            return True, msg
    return False, ''


def get_post_effect_limits(account_status):
    dapan_d7_cost_rate_14 = account_status['aveD7CostRate']  # 费率
    dapan_d7_apply_cost_14 = account_status['assessCostD7']  # 进件成本
    # cost_rate_14 = account_status['assessCostRateD14']
    print(f"大盘后效数据 14天费率: {dapan_d7_cost_rate_14}; 14天进件成本: {dapan_d7_apply_cost_14}")
    return {"dapan_d7_cost_rate_14": dapan_d7_cost_rate_14,
            "dapan_d7_apply_cost_14": dapan_d7_apply_cost_14
            }


def check_post_effect(data, adgroup_past_houxiao_data, account_id, adgroup_id, post_effect_limits, control_config,
                      data_header):
    dapan_d7_cost_rate_14, dapan_d7_apply_cost_14 = post_effect_limits['dapan_d7_cost_rate_14'], \
    post_effect_limits['dapan_d7_apply_cost_14']
    is_houxiao_stop = 0
    life_long_days = data[data_header.LIFE_LONG_DAYS]
    if (life_long_days >= 3 and data.get('past_3day_apply_pv') and data['past_3day_apply_pv'] >= control_config.POST_EFFECT_STEP_3_NUM_LIMIT and
            adgroup_past_houxiao_data['past3DaysD7ShouxinUv'] == 0):
        print(account_id, adgroup_id, life_long_days,
              '--- 3天内完件>=%d, 没有授信' * control_config.POST_EFFECT_STEP_2_NUM_LIMIT)
        is_houxiao_stop = 1
    elif life_long_days >= 7 and data.get('past_7day_apply_pv'):
        if data['past_7day_apply_pv'] >= control_config.POST_EFFECT_STEP_2_NUM_LIMIT:
            if data['past_7day_apply_cost'] > dapan_d7_apply_cost_14:
                print(account_id, adgroup_id, life_long_days, '7天内完件>=%d, 完件成本 %.2f 大于大盘 %.2f' % (
                control_config.POST_EFFECT_STEP_2_NUM_LIMIT, data['past_7day_apply_cost'],
                dapan_d7_apply_cost_14))
                is_houxiao_stop = 1
            else:
                if adgroup_past_houxiao_data['past7DaysD7ShouxinUv'] == 0:
                    print(account_id, adgroup_id, life_long_days,
                        f'--- 7天内完件>={control_config.POST_EFFECT_STEP_2_NUM_LIMIT}, 没有授信')
                    is_houxiao_stop = 1
                else:
                    if adgroup_past_houxiao_data[
                        'past7DaysD7CostRate'] > control_config.COST_RATE_SCALE * dapan_d7_cost_rate_14:
                        print(account_id, adgroup_id, life_long_days,
                            f'--- 7天内完件>={control_config.POST_EFFECT_STEP_2_NUM_LIMIT}, 有授信, 费率 %.2f大于大盘 %.2f' % (
                            adgroup_past_houxiao_data['past7DaysD7CostRate'], dapan_d7_cost_rate_14))
                        is_houxiao_stop = 1
    elif life_long_days >= 14 and data.get('past_14day_apply_pv') and data['past_14day_apply_pv'] < control_config.POST_EFFECT_STEP_2_NUM_LIMIT:
        if data['past_14day_applt_cost'] > dapan_d7_apply_cost_14:
            print(account_id, adgroup_id, life_long_days,
                  f'--- 14天内完件<{control_config.POST_EFFECT_STEP_2_NUM_LIMIT}, 完件成本 %.2f 大于大盘 %.2f' % \
                  (data['past_14day_apply_cost'], dapan_d7_apply_cost_14))
            is_houxiao_stop = 1
        else:
            if adgroup_past_houxiao_data['past2WeeksD7ShouxinUv'] == 0:
                print(account_id, adgroup_id, life_long_days,
                      f'--- 14天内完件<{control_config.POST_EFFECT_STEP_2_NUM_LIMIT}, 没有授信')
                is_houxiao_stop = 1
            else:
                if adgroup_past_houxiao_data[
                    'aveD7CostRate'] > control_config.COST_RATE_SCALE * dapan_d7_cost_rate_14:
                    print(account_id, adgroup_id, life_long_days,
                          f'--- 14天内完件<{control_config.POST_EFFECT_STEP_2_NUM_LIMIT}, 有授信, 14天费率 %.2f 大于大盘的%.2f倍' % \
                          (adgroup_past_houxiao_data['aveD7CostRate'], control_config.COST_RATE_SCALE * 1.0))
                    is_houxiao_stop = 1
    elif life_long_days >= 7 and adgroup_past_houxiao_data[
        'past7DaysD7ApplyPv'] >= control_config.POST_EFFECT_STEP_2_NUM_LIMIT * 2:
        if adgroup_past_houxiao_data['past7DaysD7ShouxinUv'] == 0:
            print(account_id, adgroup_id, life_long_days,
                  '--- 7天内进件>=%d, 没有授信' * control_config.POST_EFFECT_STEP_2_NUM_LIMIT * 2)
            is_houxiao_stop = 1
        else:
            if adgroup_past_houxiao_data[
                'past7DaysD7CostRate'] > control_config.COST_RATE_SCALE * 0.5 * dapan_d7_cost_rate_14:
                print(account_id, adgroup_id, life_long_days,
                      f'--- 7天内进件>={control_config.POST_EFFECT_STEP_2_NUM_LIMIT}, 有授信, 费率 %.2f 大于大盘的%.2f倍' % (
                      adgroup_past_houxiao_data['past7DaysD7CostRate'], control_config.COST_RATE_SCALE * 0.5))
                is_houxiao_stop = 1
    return is_houxiao_stop


def check_if_over_budget(select_account_id, adgroup_status, account_status, control_config, account_config, 
                         path_config, data_header, string_format, scale_ratio=1):
    print(
        f"前效控停配置: {control_config.STEP_1_COST_LIMIT_SCALE}-{control_config.STEP_2_COST_LIMIT_SCALE}")

    pre_effect_limits = get_pre_effect_limits(account_status, scale_ratio, control_config)

    print(f"后效控停开启：{bool(control_config.USE_HOUXIAO_STOP)}")

    print(f"后效控停配置: {control_config.POST_EFFECT_STEP_2_NUM_LIMIT}-{control_config.COST_RATE_SCALE}")

    post_effect_limits = get_post_effect_limits(account_status)

    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    miniute = int(str(now).split(' ')[1].split(':')[1])

    print('--- 保成本 ...')
    print('--- 当前时间：', day, hour, miniute)
    # 当天一键起量过的晚上6点之后判断, 当天没有起量过的, 每10分钟扫描
    res = {}
    adgroup_report_data = json.load(open(path_config.ADGROUP_REPORT_PATH, encoding='utf8'))
    for account_id in [select_account_id]:
        print('-----------------------------')
        print('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()

        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                continue
            data = adgroup_data[adgroup_id]

            adgroup_past_houxiao_data = adgroup_status[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in account_config.ACTIVE_STATUS:
                continue

            print('*** %d 当前数据 ...' % int(adgroup_id))
            today_cost = data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, data_header.COST_POSTFIX])
            today_step_1_cost = data_header.JOIN_CHAR.join(
                [data_header.TODAY_PREFIX, data_header.STEP_1_STAT_NAME, data_header.COST_POSTFIX])
            today_step_1_uv = data_header.JOIN_CHAR.join(
                [data_header.TODAY_PREFIX, data_header.STEP_1_UV])
            today_step_2_cost = data_header.JOIN_CHAR.join(
                [data_header.TODAY_PREFIX, data_header.STEP_2_STAT_NAME, data_header.COST_POSTFIX])
            today_step_2_uv = data_header.JOIN_CHAR.join(
                [data_header.TODAY_PREFIX, data_header.STEP_2_UV])
            
            msg = '--- 当天表单数 %d, 当天消耗 %.2f 当天表单成本 %.2f 限制: %d' % \
                  (data[today_step_1_uv], data[today_cost], data[today_step_1_cost],
                   pre_effect_limits['adgroup_step_1_cost_limit'])
            print(msg)
            msg = '--- 当天完件数 %d, 当天消耗 %.2f 当天完件成本 %.2f 限制: %d' % \
                  (data[today_step_2_uv], data[today_cost], data[today_step_2_cost],
                   pre_effect_limits['adgroup_step_2_cost_limit'])
            print(msg)
            weekends = [4, 5, 6] # Fri, Sat, Sun
            if datetime.datetime.today().weekday() in weekends: 
                tomorrow = str(datetime.datetime.today() + datetime.timedelta(days=7-datetime.datetime.today().weekday())).split(' ')[0]
                # print(f"--- 今天是周末，下周一是{tomorrow}")
            else:
                tomorrow = str(datetime.datetime.today() + datetime.timedelta(days=1)).split(' ')[0]

            if system_status in account_config.ACTIVE_STATUS:
                print('--- 在投放中，检查是否控停 ...')
                # 后效控停
                if control_config.USE_HOUXIAO_STOP:
                    is_houxiao_stop, new_msg = check_post_effect(data, adgroup_past_houxiao_data, account_id, adgroup_id, post_effect_limits)
                    if is_houxiao_stop:
                        select_up_adgroup_ids.append(
                            [f'{string_format.UPDATE_DELIVERY_DATE}%s' % tomorrow, account_id, int(adgroup_id), new_msg])
                
                # 前效控停
                check_cost_thres = get_check_pre_effect_thres(account_status, control_config)
                stop_pre_effect, new_msg = check_pre_effect(data, account_id, adgroup_id, check_cost_thres, pre_effect_limits, data_header)
                if stop_pre_effect:
                    select_up_adgroup_ids.append(
                        [f'{string_format.UPDATE_DELIVERY_DATE}%s' % tomorrow, account_id, int(adgroup_id), new_msg])
        print('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print(d)
        res[account_id] = select_up_adgroup_ids
    return res


def check_if_suspend(select_account_id, adgroup_status, account_status, control_config, account_config, 
                     path_config, data_header, string_format, scale_ratio=1):
    print('--- 检查是否暂停 ...')
    pre_effect_limits = get_pre_effect_limits(account_status, scale_ratio, control_config)
    post_effect_limits = get_post_effect_limits(account_status)

    suspend_date = datetime.datetime.today() + datetime.timedelta(days=30)
    # if suspend date in weekends, change to next monday
    if suspend_date.weekday() in [5, 6]:
        suspend_date = suspend_date + datetime.timedelta(days=7-suspend_date.weekday())
        # print(f"--- 暂停日期是周末，下周一{suspend_date.strftime('%Y-%m-%d')}")
    suspend_date = suspend_date.strftime('%Y-%m-%d')
    res = {}
    adgroup_report_data = json.load(open(path_config.ADGROUP_REPORT_PATH, encoding='utf8'))
    for account_id in [select_account_id]:
        print('-----------------------------')
        print('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()

        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                continue
            data = adgroup_data[adgroup_id]

            adgroup_past_houxiao_data = adgroup_status[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            # print(f"system status: {system_status}")
            if system_status not in account_config.ACTIVE_STATUS + ['ADGROUP_STATUS_NOT_IN_DELIVERY_TIME']:
                continue
            
            # 暂停
            suspend, new_msg = check_suspend(data, adgroup_past_houxiao_data, account_id, adgroup_id, pre_effect_limits, post_effect_limits, data_header)
            if suspend:
                #select_up_adgroup_ids.append(
                #    [f'{string_format.SUSPEND}', account_id, int(adgroup_id), new_msg])
                # 'suspend-{$account_id}'
                select_up_adgroup_ids.append(
                    [f'{string_format.UPDATE_DELIVERY_DATE}%s' % suspend_date, account_id, int(adgroup_id), new_msg])
        print('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print(d)
        res[account_id] = select_up_adgroup_ids
    return res
