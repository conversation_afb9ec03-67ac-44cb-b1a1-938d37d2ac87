import json
import os

# JSON configuration file path - constant
CONFIG_JSON_PATH = "/data2/yuwu/update_wyd/config/wyd_config.json"


class TargetConfig:

    # Default values  
    YEAR_TARGET = 210
    STEP_1_TARGET = 350
    STEP_2_TARGET = 800

    @classmethod
    def load_from_json(cls):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                target_config = config_data.get('target_config', {})

                cls.YEAR_TARGET = target_config.get('year_target', cls.YEAR_TARGET)
                cls.STEP_1_TARGET = target_config.get('step_1_target', cls.STEP_1_TARGET)
                cls.STEP_2_TARGET = target_config.get('step_2_target', cls.STEP_2_TARGET)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load target config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for TargetConfig")


class AccountConfig:

    # Default values  
    OLD_ACCOUNTS = []
    MEDIAN_ACCOUNTS = []
    NEW_ACCOUNTS = ['********']
    ONE_HUNDRED_ACCOUNT = []
    ONE_THIRD_ACCOUNT = []
    TWO_SMALL_UP_ACCOUNT = []
    TWO_UPS_ACCOUNT = []
    AFTERNOON_ACCOUNTS = []
    CONTROL_EXCLUDE_ACCOUNTS = []
    CREATE_EXCLUDE_ACCOUNTS = []
    UP_EXCLUDE_ACCOUNTS = []
    OVER_BUDGET_EXCLUDE_ACCOUNTS = []
    LOW_COST_EXCLUDE_ACCOUNTS = []
    YOUJU_ACCOUNTS = []
    STOP_ACCOUNTS = []
    ACTIVE_STATUS = ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']
    NOT_IN_DELIVERY_STATUS = 'ADGROUP_STATUS_NOT_IN_DELIVERY'
    AUTO_ACQUISITION_PENDING_STATUS = 'AUTO_ACQUISITION_STATUS_PENDING'

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                account_config = config_data.get('account_config', {})

                cls.OLD_ACCOUNTS = account_config.get('old_accounts', cls.OLD_ACCOUNTS)
                cls.MEDIAN_ACCOUNTS = account_config.get('median_accounts', cls.MEDIAN_ACCOUNTS)
                cls.NEW_ACCOUNTS = account_config.get('new_accounts', cls.NEW_ACCOUNTS)
                cls.ONE_HUNDRED_ACCOUNT = account_config.get('one_hundred_account', cls.ONE_HUNDRED_ACCOUNT)
                cls.ONE_THIRD_ACCOUNT = account_config.get('one_third_account', cls.ONE_THIRD_ACCOUNT)
                cls.TWO_SMALL_UP_ACCOUNT = account_config.get('two_small_up_account', cls.TWO_SMALL_UP_ACCOUNT)
                cls.TWO_UPS_ACCOUNT = account_config.get('two_ups_account', cls.TWO_UPS_ACCOUNT)
                cls.AFTERNOON_ACCOUNTS = account_config.get('afternoon_accounts', cls.AFTERNOON_ACCOUNTS)
                cls.CONTROL_EXCLUDE_ACCOUNTS = account_config.get('control_exclude_accounts', cls.CONTROL_EXCLUDE_ACCOUNTS)
                cls.CREATE_EXCLUDE_ACCOUNTS = account_config.get('create_exclude_accounts', cls.CREATE_EXCLUDE_ACCOUNTS)
                cls.UP_EXCLUDE_ACCOUNTS = account_config.get('up_exclude_accounts', cls.UP_EXCLUDE_ACCOUNTS)
                cls.OVER_BUDGET_EXCLUDE_ACCOUNTS = account_config.get('over_budget_exclude_accounts', cls.OVER_BUDGET_EXCLUDE_ACCOUNTS)
                cls.LOW_COST_EXCLUDE_ACCOUNTS = account_config.get('low_cost_exclude_accounts', cls.LOW_COST_EXCLUDE_ACCOUNTS)
                cls.YOUJU_ACCOUNTS = account_config.get('youju_accounts', cls.YOUJU_ACCOUNTS)
                cls.STOP_ACCOUNTS = account_config.get('stop_accounts', cls.STOP_ACCOUNTS)
                cls.ACTIVE_STATUS = account_config.get('active_status', cls.ACTIVE_STATUS)
                cls.NOT_IN_DELIVERY_STATUS = account_config.get('not_in_delivery_status', cls.NOT_IN_DELIVERY_STATUS)
                cls.AUTO_ACQUISITION_PENDING_STATUS = account_config.get('auto_acquisition_pending_status', cls.AUTO_ACQUISITION_PENDING_STATUS)

                # Computed property
                cls.ACCOUNT_IDS = cls.OLD_ACCOUNTS + cls.MEDIAN_ACCOUNTS + cls.NEW_ACCOUNTS

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load account config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for AccountConfig")
            # Ensure computed property is set even with defaults
            cls.ACCOUNT_IDS = cls.OLD_ACCOUNTS + cls.MEDIAN_ACCOUNTS + cls.NEW_ACCOUNTS


class APIConfig:
    # Default values  
    API_URL = 'https://api.e.qq.com/v3.0/'
    ADGROUPS_INTERFACE = 'adgroups/get'
    HOURLY_INTERFACE = 'hourly_reports/get'
    DAILY_INTERFACE = 'daily_reports/get'
    ACCESS_TOKEN = '66c1748bdca5e0016250f3cdd4cdaa4f'
    SERVER_PORT = 8866  # Default server port


    @classmethod
    def load_from_json(cls):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                api_config = config_data.get('api_config', {})

                cls.API_URL = api_config.get('api_url', cls.API_URL)
                cls.ADGROUPS_INTERFACE = api_config.get('adgroups_interface', cls.ADGROUPS_INTERFACE)
                cls.HOURLY_INTERFACE = api_config.get('hourly_interface', cls.HOURLY_INTERFACE)
                cls.DAILY_INTERFACE = api_config.get('daily_interface', cls.DAILY_INTERFACE)
                cls.ACCESS_TOKEN = api_config.get('access_token', cls.ACCESS_TOKEN)
                cls.SERVER_PORT = api_config.get('server_port', cls.SERVER_PORT)


        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load API config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for APIConfig")


class PathConfig:

    # Default values  
    ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
    ADGROUPS_DIR = "./adgroups"
    ADGROUP_REPORT_PATH = 'adgroup_report_data.json'
    ACCOUNT_REPORT_PATH = 'account_report_data.json'
    STOP_DATES_PATH = 'stop_dates.txt'
    ACCOUNT_UP_TIME_PATH = 'account_up_time.json'
    ACCOUNT_CREATE_TIME_PATH = 'account_create_time.json'

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                path_config = config_data.get('path_config', {})

                cls.ADGROUPS_DIR = path_config.get('adgroups_dir', cls.ADGROUPS_DIR)
                cls.ADGROUP_REPORT_PATH = path_config.get('adgroup_report_path', cls.ADGROUP_REPORT_PATH)
                cls.ACCOUNT_REPORT_PATH = path_config.get('account_report_path', cls.ACCOUNT_REPORT_PATH)
                cls.STOP_DATES_PATH = path_config.get('stop_dates_path', cls.STOP_DATES_PATH)
                # ROOT_DIR is computed, not loaded from JSON

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load path config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for PathConfig")


class CreateConfig:

    # Default values  
    NO_CREATE_WEEK_OF_DAY = ['星期二', '星期四', '星期六', '星期日']

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                create_config = config_data.get('create_config', {})

                cls.NO_CREATE_WEEK_OF_DAY = create_config.get('no_create_week_of_day', cls.NO_CREATE_WEEK_OF_DAY)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load create config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for CreateConfig")


class BudgetControlConfig:
    # Default values  
    CHECK_PRE_THRESHOLD_RATIO = 0.5
    STEP_1_COST_LIMIT_SCALE = 2
    STEP_2_COST_LIMIT_SCALE = 2

    USE_HOUXIAO_STOP = 0
    COST_RATE_SCALE = 2
    MORNING_SCALE = 1.0
    AFTERNOON_SCALE = 1.0
    NIGHT_SCALE = 1.0
    POST_EFFECT_STEP_2_NUM_LIMIT = 10

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                budget_config = config_data.get('budget_control_config', {})

                cls.CHECK_PRE_THRESHOLD_RATIO = budget_config.get('check_pre_threshold_ratio', cls.CHECK_PRE_THRESHOLD_RATIO)
                cls.STEP_1_COST_LIMIT_SCALE = budget_config.get('step_1_cost_limit_scale', cls.STEP_1_COST_LIMIT_SCALE)
                cls.STEP_2_COST_LIMIT_SCALE = budget_config.get('step_2_cost_limit_scale', cls.STEP_2_COST_LIMIT_SCALE)
                cls.USE_HOUXIAO_STOP = budget_config.get('use_houxiao_stop', cls.USE_HOUXIAO_STOP)
                cls.COST_RATE_SCALE = budget_config.get('cost_rate_scale', cls.COST_RATE_SCALE)
                cls.MORNING_SCALE = budget_config.get('morning_scale', cls.MORNING_SCALE)
                cls.AFTERNOON_SCALE = budget_config.get('afternoon_scale', cls.AFTERNOON_SCALE)
                cls.NIGHT_SCALE = budget_config.get('night_scale', cls.NIGHT_SCALE)
                cls.POST_EFFECT_STEP_2_NUM_LIMIT = budget_config.get('post_effect_step_2_num_limit', cls.POST_EFFECT_STEP_2_NUM_LIMIT)


        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load budget control config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for BudgetControlConfig")


class AcquisitionControlConfig:
    # Default values  
    HIST_RANGE = 7
    UP_ADGROUP_NUM_FOR_MORNING = 1 # 所有账号只起量一个广告
    UP_ADGROUP_NUM_FOR_AFTERNOON = 1 # 所有账号只起量一个广告
    UP_ADGROUP_NUM_FOR_MODEL = 2 
    UP_NEW_ADGROUP_NUM = 1 # 起量新广告数
    UP_ALL_NUM_FOR_NEW = 1
    UP_ALL_NUM_FOR_MEDIAN = 2
    UP_ALL_NUM_FOR_OLD = 3
    MORNING_HOURS = [10, 11]
    AFTERNOON_HOURS = [14, 15]
    NO_UP_DAY_OF_WEEK = ['星期六', '星期日']
    REUP_HOURS = [12, 13, 14, 15]
    COST_THRESHOLD = 700
    ACQUISITION_BUDGET_FOR_OLD = 2000000
    ACQUISITION_BUDGET_FOR_MED = 5000000
    ACQUISITION_BUDGET_FOR_NEW = 3000000
    STEP_1_COST_LIMIT_SCALE_FOR_MORNING = 2
    STEP_2_COST_LIMIT_SCALE_FOR_MORNING = 2
    STEP_1_COST_LIMIT_SCALE_FOR_AFTERNOON = 2
    STEP_2_COST_LIMIT_SCALE_FOR_AFTERNOON = 2
    FINAL_COST_LIMIT_SCALE = 1.2

    @classmethod
    def load_from_json(cls):

        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                acquisition_config = config_data.get('acquisition_control_config', {})

                cls.HIST_RANGE = acquisition_config.get('hist_range', cls.HIST_RANGE)
                cls.UP_ALL_NUM_FOR_NEW = acquisition_config.get('up_all_num_for_new', cls.UP_ALL_NUM_FOR_NEW)
                cls.UP_ALL_NUM_FOR_MEDIAN = acquisition_config.get('up_all_num_for_median', cls.UP_ALL_NUM_FOR_MEDIAN)
                cls.UP_ALL_NUM_FOR_OLD = acquisition_config.get('up_all_num_for_old', cls.UP_ALL_NUM_FOR_OLD)
                cls.MORNING_HOURS = acquisition_config.get('morning_hours', cls.MORNING_HOURS)
                cls.AFTERNOON_HOURS = acquisition_config.get('afternoon_hours', cls.AFTERNOON_HOURS)
                cls.NO_UP_DAY_OF_WEEK = acquisition_config.get('no_up_day_of_week', cls.NO_UP_DAY_OF_WEEK)
                cls.REUP_HOURS = acquisition_config.get('reup_hours', cls.REUP_HOURS)
                cls.COST_THRESHOLD = acquisition_config.get('cost_threshold', cls.COST_THRESHOLD)
                cls.ACQUISITION_BUDGET_FOR_OLD = acquisition_config.get('acquisition_budget_for_old', cls.ACQUISITION_BUDGET_FOR_OLD)
                cls.ACQUISITION_BUDGET_FOR_MED = acquisition_config.get('acquisition_budget_for_med', cls.ACQUISITION_BUDGET_FOR_MED)
                cls.ACQUISITION_BUDGET_FOR_NEW = acquisition_config.get('acquisition_budget_for_new', cls.ACQUISITION_BUDGET_FOR_NEW)
                cls.STEP_1_COST_LIMIT_SCALE_FOR_MORNING = acquisition_config.get('step_1_cost_limit_scale_for_morning', cls.STEP_1_COST_LIMIT_SCALE_FOR_MORNING)
                cls.STEP_2_COST_LIMIT_SCALE_FOR_MORNING = acquisition_config.get('step_2_cost_limit_scale_for_morning', cls.STEP_2_COST_LIMIT_SCALE_FOR_MORNING)
                cls.STEP_1_COST_LIMIT_SCALE_FOR_AFTERNOON = acquisition_config.get('step_1_cost_limit_scale_for_afternoon', cls.STEP_1_COST_LIMIT_SCALE_FOR_AFTERNOON)
                cls.STEP_2_COST_LIMIT_SCALE_FOR_AFTERNOON = acquisition_config.get('step_2_cost_limit_scale_for_afternoon', cls.STEP_2_COST_LIMIT_SCALE_FOR_AFTERNOON)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load acquisition control config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for AcquisitionControlConfig")


class DataHeader:
    # Default values  
    LIFE_LONG_DAYS = 'life_long_days'
    UP_TREND = 'up_trend'
    IS_EVER_UP = 'is_ever_up'
    TODAY_PREFIX = 'today'
    PAST_1_DAYS_PREFIX = 'past_1day'
    PAST_3_DAYS_PREFIX = 'past_3day'
    PAST_7_DAYS_PREFIX = 'past_7day'
    PAST_14_DAYS_PREFIX = 'past_14day'
    COST_POSTFIX = 'cost'
    UV_POSTFIX = 'uv'
    STEP_1_STAT_NAME = 'reservation'
    STEP_2_STAT_NAME = 'apply'
    STEP_1_UV = 'reservation_uv'
    STEP_2_UV = 'apply_pv'
    JOIN_CHAR = '_'

    @classmethod
    def load_from_json(cls):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                data_header_config = config_data.get('data_header_config', {})

                cls.LIFE_LONG_DAYS = data_header_config.get('life_long_days', cls.LIFE_LONG_DAYS)
                cls.UP_TREND = data_header_config.get('up_trend', cls.UP_TREND)
                cls.IS_EVER_UP = data_header_config.get('is_ever_up', cls.IS_EVER_UP)
                cls.TODAY_PREFIX = data_header_config.get('today_prefix', cls.TODAY_PREFIX)
                cls.PAST_1_DAYS_PREFIX = data_header_config.get('past_1_days_prefix', cls.PAST_1_DAYS_PREFIX)
                cls.PAST_3_DAYS_PREFIX = data_header_config.get('past_3_days_prefix', cls.PAST_3_DAYS_PREFIX)
                cls.PAST_7_DAYS_PREFIX = data_header_config.get('past_7_days_prefix', cls.PAST_7_DAYS_PREFIX)
                cls.PAST_14_DAYS_PREFIX = data_header_config.get('past_14_days_prefix', cls.PAST_14_DAYS_PREFIX)
                cls.COST_POSTFIX = data_header_config.get('cost_postfix', cls.COST_POSTFIX)
                cls.UV_POSTFIX = data_header_config.get('uv_postfix', cls.UV_POSTFIX)
                cls.STEP_1_STAT_NAME = data_header_config.get('step_1_stat_name', cls.STEP_1_STAT_NAME)
                cls.STEP_2_STAT_NAME = data_header_config.get('step_2_stat_name', cls.STEP_2_STAT_NAME)
                cls.STEP_1_UV = data_header_config.get('step_1_uv', cls.STEP_1_UV)
                cls.STEP_2_UV = data_header_config.get('step_2_uv', cls.STEP_2_UV)
                cls.JOIN_CHAR = data_header_config.get('join_char', cls.JOIN_CHAR)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load data header config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for DataHeader")


class StringFormat:
    # Default values  
    UPDATE_DELIVERY_DATE = 'updateDeliveryDate-'
    SUSPEND = 'suspend'

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                string_format_config = config_data.get('string_format_config', {})

                cls.UPDATE_DELIVERY_DATE = string_format_config.get('update_delivery_date', cls.UPDATE_DELIVERY_DATE)
                cls.SUSPEND = string_format_config.get('suspend', cls.SUSPEND)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load string format config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for StringFormat")


class ThresholdConfig:
    # Default values
    MIN_COST_THRESHOLD = 100  # Minimum cost threshold for various checks
    LOW_COST_THRESHOLD = 500  # Low cost threshold for weekend checks
    # DELETE_COST_THRESHOLD = 500  # Cost threshold for deletion decisions
    ACQUISITION_COST_THRESHOLD = 100  # Minimum acquisition cost to be considered "up"
    SIGNATURE_COST_THRESHOLD = 1000  # Minimum cost for signature data
    PAGE_SIZE_DEFAULT = 100  # Default page size for API calls
    PAGE_SIZE_LARGE = 500  # Large page size for hourly reports
    WEEKDAY_DICT = {
        0: "星期一",
        1: "星期二",
        2: "星期三",
        3: "星期四",
        4: "星期五",
        5: "星期六",
        6: "星期日"
    }
    NO_UP_DAY_OF_WEEK = ['星期六', '星期日']

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                threshold_config = config_data.get('threshold_config', {})

                cls.MIN_COST_THRESHOLD = threshold_config.get('min_cost_threshold', cls.MIN_COST_THRESHOLD)
                cls.LOW_COST_THRESHOLD = threshold_config.get('low_cost_threshold', cls.LOW_COST_THRESHOLD)
                # cls.DELETE_COST_THRESHOLD = threshold_config.get('delete_cost_threshold', cls.DELETE_COST_THRESHOLD)
                cls.ACQUISITION_COST_THRESHOLD = threshold_config.get('acquisition_cost_threshold', cls.ACQUISITION_COST_THRESHOLD)
                cls.SIGNATURE_COST_THRESHOLD = threshold_config.get('signature_cost_threshold', cls.SIGNATURE_COST_THRESHOLD)
                cls.PAGE_SIZE_DEFAULT = threshold_config.get('page_size_default', cls.PAGE_SIZE_DEFAULT)
                cls.PAGE_SIZE_LARGE = threshold_config.get('page_size_large', cls.PAGE_SIZE_LARGE)
                cls.WEEKDAY_DICT = threshold_config.get('weekday_dict', cls.WEEKDAY_DICT)
                cls.NO_UP_DAY_OF_WEEK = threshold_config.get('no_up_day_of_week', cls.NO_UP_DAY_OF_WEEK)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load threshold config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for ThresholdConfig")


def load_all_configs():
    print(f"Loading all configurations from {CONFIG_JSON_PATH}")
    TargetConfig.load_from_json()
    AccountConfig.load_from_json()
    APIConfig.load_from_json()
    PathConfig.load_from_json()
    CreateConfig.load_from_json()
    BudgetControlConfig.load_from_json()
    AcquisitionControlConfig.load_from_json()
    DataHeader.load_from_json()
    StringFormat.load_from_json()
    ThresholdConfig.load_from_json()
    print("All configurations loaded successfully")


# Auto-load configurations when module is imported
load_all_configs()
