import pandas as pd
import numpy as np
import sys
import os


def main():
    sys.path.append('/data2/yuwu/wyd_mini_app')

    from budget_control_by_model import init_model, get_predict_data, get_daily_data
    # 1. load model
    model_path = '/0_day_model.json'
    model = init_model(model_path)
    print("init model success")

    data_path = '/data2/xizhang/auto_bid/wyd_mini_app/data_dynamic_creative_level/hourly_reports_REQUEST_TIME'
    account_ids = []
    # loop through the data path, get all account ids
    for file in os.listdir(data_path):
        if file.endswith('.csv'):
            account_id = file.split('.')[0]
            account_ids.append(int(account_id))
    get_daily_data(account_ids)

    # data_path = '../adgroup_daily/'
    df = pd.read_csv(os.path.join(data_path, '********.csv'))
    predict_df = get_predict_data(model, df)
    print("get predict data success")

    adgroup_ids = predict_df['adgroup_id'].unique()
    adgroup_status = {adgroup_id: {'systemStatus': 'ADGROUP_STATUS_ACTIVE'} for adgroup_id in adgroup_ids}

    # 调用接口样例：
    sys.path.append('/data2/yuwu')
    from wyd_mini_app.budget_control.config import StringFormat, AccountConfig
    from wyd_mini_app.budget_control.budget_control_by_model import check_if_over_budget_by_model, get_daily_data
    """
    每天更新历史日报：get_daily_data
    """
    get_daily_data(account_ids)
    """
    select_account_id: str
    adgroup_status: dict, adgroup info
    account_config: AccountConfig object
    string_format: StringFormat object
    score_thres: float, 模型分数阈值, 默认值0.8
    """
    res = check_if_over_budget_by_model(********, adgroup_status, AccountConfig, StringFormat)


if __name__ == "__main__":
    main()